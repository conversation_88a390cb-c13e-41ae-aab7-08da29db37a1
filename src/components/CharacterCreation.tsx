import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

export function CharacterCreation() {
  const [name, setName] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const createCharacter = useMutation(api.characters.createCharacter);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) {
      toast.error("Please enter a character name");
      return;
    }

    setIsCreating(true);
    try {
      await create<PERSON>haracter({ name: name.trim() });
      toast.success("Character created successfully!");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to create character");
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="max-w-md mx-auto bg-black/30 backdrop-blur-sm rounded-lg p-8 border border-white/20">
      <div className="text-center mb-6">
        <h2 className="text-3xl font-bold text-white mb-2">Create Your Hero</h2>
        <p className="text-gray-300">Choose a name for your adventure</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
            Character Name
          </label>
          <input
            type="text"
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-400 focus:border-blue-400 focus:ring-1 focus:ring-blue-400 outline-none transition-all"
            placeholder="Enter your hero's name"
            maxLength={20}
            disabled={isCreating}
          />
        </div>

        <button
          type="submit"
          disabled={isCreating || !name.trim()}
          className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
        >
          {isCreating ? "Creating Hero..." : "Begin Adventure"}
        </button>
      </form>
    </div>
  );
}
