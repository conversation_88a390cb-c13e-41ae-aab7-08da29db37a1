import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

export function CombatArea() {
  const areas = useQuery(api.areas.getAvailableAreas);
  const [selectedAreaId, setSelectedAreaId] = useState<string | null>(null);
  const monsters = useQuery(api.areas.getAreaMonsters, 
    selectedAreaId ? { areaId: selectedAreaId as any } : "skip"
  );
  const combatCooldown = useQuery(api.combat.getCombatCooldown);
  const initiateCombat = useMutation(api.combat.initiateCombat);
  const [isInCombat, setIsInCombat] = useState(false);
  const [cooldownTimer, setCooldownTimer] = useState(0);

  // Update cooldown timer
  useEffect(() => {
    if (combatCooldown?.isOnCooldown && combatCooldown.remainingTime > 0) {
      setCooldownTimer(combatCooldown.remainingTime);
      
      const interval = setInterval(() => {
        setCooldownTimer(prev => {
          if (prev <= 1) {
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(interval);
    } else {
      setCooldownTimer(0);
    }
  }, [combatCooldown]);

  const handleCombat = async (monsterId: string) => {
    setIsInCombat(true);
    try {
      const result = await initiateCombat({ monsterId: monsterId as any });
      
      let message = `⚔️ Attacked ${result.monster}! `;
      message += `Dealt ${result.damage} damage${result.isCritical ? " (CRITICAL!)" : ""}. `;
      
      if (result.monsterDefeated) {
        message += `💀 Monster defeated! `;
        message += `Gained ${result.experienceGained} XP and ${result.goldGained} gold.`;
      } else {
        message += `Monster has ${result.monsterRemainingHealth} HP left. `;
        if (result.characterTookDamage > 0) {
          message += `💔 You took ${result.characterTookDamage} damage! `;
        }
        message += `Gained ${result.experienceGained} XP and ${result.goldGained} gold.`;
      }
      
      if (result.leveledUp) {
        message += ` 🎉 LEVEL UP! Now level ${result.newLevel}!`;
      }

      if (result.characterHealth <= 0) {
        message += ` ☠️ You have been defeated! Heal before continuing.`;
        toast.error(message);
      } else if (result.monsterDefeated) {
        toast.success(message);
      } else {
        toast.info(message);
      }

      // Force cooldown timer to start immediately after successful combat
      setCooldownTimer(3);
      const interval = setInterval(() => {
        setCooldownTimer(prev => {
          if (prev <= 1) {
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Combat failed");
    } finally {
      setIsInCombat(false);
    }
  };

  if (!areas) {
    return (
      <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <div className="animate-pulse">Loading areas...</div>
      </div>
    );
  }

  const isOnCooldown = cooldownTimer > 0 || isInCombat;

  return (
    <div className="space-y-6">
      {/* Area Selection */}
      <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <h3 className="text-xl font-bold text-white mb-4">🗺️ Select Area</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {areas.map((area) => (
            <button
              key={area._id}
              onClick={() => setSelectedAreaId(area._id)}
              disabled={!area.isAccessible}
              className={`p-4 rounded-lg border transition-all text-left ${
                selectedAreaId === area._id
                  ? "bg-blue-600/30 border-blue-400 text-white"
                  : area.isAccessible
                  ? "bg-white/5 border-white/20 text-gray-300 hover:bg-white/10"
                  : "bg-red-900/20 border-red-500/50 text-red-400 cursor-not-allowed"
              }`}
            >
              <div className="font-semibold">{area.name}</div>
              <div className="text-sm opacity-75">
                Level {area.recommendedLevel}
                {!area.isAccessible && " (Locked)"}
              </div>
              <div className="text-xs mt-1 opacity-60">{area.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Cooldown Indicator */}
      {cooldownTimer > 0 && (
        <div className="bg-yellow-900/30 backdrop-blur-sm rounded-lg p-4 border border-yellow-500/50">
          <div className="flex items-center justify-center space-x-2">
            <span className="text-yellow-400">⏱️</span>
            <span className="text-yellow-300">
              Combat cooldown: {cooldownTimer} second{cooldownTimer !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
      )}

      {/* Monster Selection */}
      {selectedAreaId && monsters && (
        <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <h3 className="text-xl font-bold text-white mb-4">👹 Choose Your Enemy</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {monsters.map((monster) => (
              <div
                key={monster._id}
                className={`p-4 rounded-lg border ${
                  monster.difficulty === "easy"
                    ? "border-green-500/50 bg-green-900/20"
                    : monster.difficulty === "hard"
                    ? "border-red-500/50 bg-red-900/20"
                    : "border-yellow-500/50 bg-yellow-900/20"
                }`}
              >
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-semibold text-white">{monster.name}</h4>
                    <p className="text-sm text-gray-300">Level {monster.level}</p>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    monster.difficulty === "easy"
                      ? "bg-green-600 text-white"
                      : monster.difficulty === "hard"
                      ? "bg-red-600 text-white"
                      : "bg-yellow-600 text-white"
                  }`}>
                    {monster.difficulty}
                  </span>
                </div>
                
                <div className="text-sm text-gray-300 space-y-1 mb-4">
                  <div className="flex justify-between">
                    <span>❤️ Health:</span>
                    <span className="text-white">
                      {monster.health}
                    </span>
                  </div>
                  <div>⚔️ Attack: {monster.attack}</div>
                  <div>🛡️ Defense: {monster.defense}</div>
                  <div>⭐ XP: {monster.experience}</div>
                  <div>💰 Gold: {monster.goldReward}</div>
                </div>

                <button
                  onClick={() => handleCombat(monster._id)}
                  disabled={isOnCooldown}
                  className={`w-full px-4 py-2 rounded-lg transition-colors font-medium ${
                    isOnCooldown
                      ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                      : "bg-red-600 text-white hover:bg-red-700"
                  }`}
                >
                  {isInCombat 
                    ? "Fighting..." 
                    : cooldownTimer > 0 
                    ? `Cooldown (${cooldownTimer}s)` 
                    : "⚔️ Attack"
                  }
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
