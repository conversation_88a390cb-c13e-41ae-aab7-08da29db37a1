import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getCharacterInventory = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) return [];

    const characterItems = await ctx.db
      .query("characterItems")
      .withIndex("by_character", (q) => q.eq("characterId", character._id))
      .collect();

    const inventoryWithItems = await Promise.all(
      characterItems.map(async (characterItem) => {
        const item = await ctx.db.get(characterItem.itemId);
        return {
          ...characterItem,
          item,
        };
      })
    );

    return inventoryWithItems.filter(item => item.item !== null);
  },
});

export const equipItem = mutation({
  args: { 
    characterItemId: v.id("characterItems"),
    equipSlot: v.union(
      v.literal("weapon"),
      v.literal("helmet"),
      v.literal("chest"),
      v.literal("legs"),
      v.literal("boots"),
      v.literal("gloves"),
      v.literal("ring1"),
      v.literal("ring2"),
      v.literal("necklace")
    ),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) throw new Error("Character not found");

    const characterItem = await ctx.db.get(args.characterItemId);
    if (!characterItem || characterItem.characterId !== character._id) {
      throw new Error("Item not found or not owned");
    }

    const item = await ctx.db.get(characterItem.itemId);
    if (!item) throw new Error("Item data not found");

    // Validate item type matches slot
    const validSlots = {
      weapon: ["weapon"],
      helmet: ["armor"],
      chest: ["armor"],
      legs: ["armor"],
      boots: ["armor"],
      gloves: ["armor"],
      ring1: ["accessory"],
      ring2: ["accessory"],
      necklace: ["accessory"],
    };

    if (!validSlots[args.equipSlot].includes(item.type)) {
      throw new Error("Item cannot be equipped in this slot");
    }

    // Unequip any item currently in this slot
    const currentlyEquipped = await ctx.db
      .query("characterItems")
      .withIndex("by_character_equipped", (q) => 
        q.eq("characterId", character._id).eq("isEquipped", true)
      )
      .collect();

    for (const equipped of currentlyEquipped) {
      if (equipped.equipSlot === args.equipSlot) {
        await ctx.db.patch(equipped._id, {
          isEquipped: false,
          equipSlot: undefined,
        });
      }
    }

    // Equip the new item
    await ctx.db.patch(characterItem._id, {
      isEquipped: true,
      equipSlot: args.equipSlot,
    });

    return true;
  },
});

export const unequipItem = mutation({
  args: { characterItemId: v.id("characterItems") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) throw new Error("Character not found");

    const characterItem = await ctx.db.get(args.characterItemId);
    if (!characterItem || characterItem.characterId !== character._id) {
      throw new Error("Item not found or not owned");
    }

    await ctx.db.patch(characterItem._id, {
      isEquipped: false,
      equipSlot: undefined,
    });

    return true;
  },
});
