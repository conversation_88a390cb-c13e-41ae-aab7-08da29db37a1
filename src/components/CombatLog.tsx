import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export function CombatLog() {
  const combatHistory = useQuery(api.combat.getCombatHistory);

  if (!combatHistory) {
    return (
      <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <div className="animate-pulse">Loading combat log...</div>
      </div>
    );
  }

  return (
    <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
      <h3 className="text-xl font-bold text-white mb-4">📜 Combat Log</h3>
      
      {combatHistory.length === 0 ? (
        <p className="text-gray-400">No combat history yet</p>
      ) : (
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {combatHistory.map((log) => (
            <div
              key={log._id}
              className="p-3 bg-white/5 rounded-lg border border-white/10"
            >
              <div className="flex justify-between items-start mb-2">
                <span className="font-medium text-white">{log.monsterName}</span>
                <span className="text-xs text-gray-400">
                  {new Date(log.timestamp).toLocaleTimeString()}
                </span>
              </div>
              
              <div className="text-sm space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-300">Damage:</span>
                  <span className={`font-bold ${log.isCritical ? "text-red-400" : "text-orange-400"}`}>
                    {log.damage}{log.isCritical && " CRIT!"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">XP:</span>
                  <span className="text-yellow-400">+{log.experienceGained}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Gold:</span>
                  <span className="text-yellow-400">+{log.goldGained}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
