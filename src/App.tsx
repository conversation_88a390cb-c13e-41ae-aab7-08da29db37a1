import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import { GameDashboard } from "./components/GameDashboard";
import { CharacterCreation } from "./components/CharacterCreation";

export default function App() {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <header className="sticky top-0 z-10 bg-black/20 backdrop-blur-sm h-16 flex justify-between items-center border-b border-white/10 shadow-lg px-4">
        <h2 className="text-2xl font-bold text-white">⚔️ RPG Adventure</h2>
        <Authenticated>
          <SignOutButton />
        </Authenticated>
      </header>
      <main className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-6xl mx-auto">
          <Content />
        </div>
      </main>
      <Toaster />
    </div>
  );
}

function Content() {
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const character = useQuery(api.characters.getCurrentCharacter);

  if (loggedInUser === undefined || character === undefined) {
    return (
      <div className="flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-8">
      <Unauthenticated>
        <div className="text-center mb-8">
          <h1 className="text-6xl font-bold text-white mb-4">⚔️ RPG Adventure</h1>
          <p className="text-xl text-gray-300">Enter a world of magic and monsters</p>
        </div>
        <div className="max-w-md mx-auto">
          <SignInForm />
        </div>
      </Unauthenticated>

      <Authenticated>
        {!character ? (
          <CharacterCreation />
        ) : (
          <GameDashboard />
        )}
      </Authenticated>
    </div>
  );
}
