import { mutation } from "./_generated/server";

export const seedGameData = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if data already exists
    const existingAreas = await ctx.db.query("areas").collect();
    if (existingAreas.length > 0) {
      return "Data already seeded";
    }

    // Create areas
    const forestId = await ctx.db.insert("areas", {
      name: "Whispering Forest",
      description: "A peaceful forest perfect for beginners",
      minLevel: 1,
      maxLevel: 5,
    });

    const cavesId = await ctx.db.insert("areas", {
      name: "Dark Caves",
      description: "Dangerous caves filled with stronger monsters",
      minLevel: 5,
      maxLevel: 10,
    });

    const mountainsId = await ctx.db.insert("areas", {
      name: "Frozen Mountains",
      description: "Treacherous peaks for experienced adventurers",
      minLevel: 10,
      maxLevel: 20,
    });

    // Create monsters for Whispering Forest
    await ctx.db.insert("monsters", {
      name: "Forest Goblin",
      areaId: forestId,
      level: 1,
      health: 25,
      maxHealth: 25,
      attack: 8,
      defense: 2,
      experience: 15,
      goldReward: 5,
    });

    await ctx.db.insert("monsters", {
      name: "Wild Wolf",
      areaId: forestId,
      level: 3,
      health: 45,
      maxHealth: 45,
      attack: 12,
      defense: 4,
      experience: 25,
      goldReward: 8,
    });

    await ctx.db.insert("monsters", {
      name: "Forest Troll",
      areaId: forestId,
      level: 5,
      health: 80,
      maxHealth: 80,
      attack: 18,
      defense: 8,
      experience: 50,
      goldReward: 15,
    });

    // Create monsters for Dark Caves
    await ctx.db.insert("monsters", {
      name: "Cave Bat",
      areaId: cavesId,
      level: 5,
      health: 60,
      maxHealth: 60,
      attack: 15,
      defense: 5,
      experience: 40,
      goldReward: 12,
    });

    await ctx.db.insert("monsters", {
      name: "Stone Golem",
      areaId: cavesId,
      level: 8,
      health: 120,
      maxHealth: 120,
      attack: 25,
      defense: 15,
      experience: 80,
      goldReward: 25,
    });

    await ctx.db.insert("monsters", {
      name: "Cave Dragon",
      areaId: cavesId,
      level: 10,
      health: 200,
      maxHealth: 200,
      attack: 35,
      defense: 20,
      experience: 150,
      goldReward: 50,
    });

    // Create monsters for Frozen Mountains
    await ctx.db.insert("monsters", {
      name: "Ice Elemental",
      areaId: mountainsId,
      level: 12,
      health: 180,
      maxHealth: 180,
      attack: 30,
      defense: 18,
      experience: 120,
      goldReward: 40,
    });

    await ctx.db.insert("monsters", {
      name: "Frost Giant",
      areaId: mountainsId,
      level: 15,
      health: 300,
      maxHealth: 300,
      attack: 45,
      defense: 25,
      experience: 200,
      goldReward: 75,
    });

    await ctx.db.insert("monsters", {
      name: "Ancient Yeti",
      areaId: mountainsId,
      level: 20,
      health: 500,
      maxHealth: 500,
      attack: 60,
      defense: 35,
      experience: 350,
      goldReward: 150,
    });

    // Create some basic items
    const swordId = await ctx.db.insert("items", {
      name: "Iron Sword",
      type: "weapon",
      rarity: "common",
      level: 1,
      stats: {
        attack: 10,
        strength: 2,
      },
      value: 50,
      description: "A sturdy iron sword for beginners",
    });

    const armorId = await ctx.db.insert("items", {
      name: "Leather Armor",
      type: "armor",
      rarity: "common",
      level: 1,
      stats: {
        defense: 5,
        health: 20,
      },
      value: 40,
      description: "Basic leather armor for protection",
    });

    const ringId = await ctx.db.insert("items", {
      name: "Ring of Luck",
      type: "accessory",
      rarity: "uncommon",
      level: 3,
      stats: {
        luck: 5,
      },
      value: 100,
      description: "A magical ring that brings good fortune",
    });

    return "Game data seeded successfully!";
  },
});
