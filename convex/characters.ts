import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getCurrentCharacter = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return character;
  },
});

export const createCharacter = mutation({
  args: {
    name: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    // Check if user already has a character
    const existingCharacter = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (existingCharacter) {
      throw new Error("Character already exists");
    }

    const characterId = await ctx.db.insert("characters", {
      userId,
      name: args.name,
      level: 1,
      experience: 0,
      experienceToNext: 100,
      health: 100,
      maxHealth: 100,
      mana: 50,
      maxMana: 50,
      strength: 10,
      defense: 10,
      agility: 10,
      intelligence: 10,
      luck: 10,
      statPoints: 0,
      gold: 100,
      gems: 0,
      isInCombat: false,
    });

    return characterId;
  },
});

export const allocateStatPoints = mutation({
  args: {
    strength: v.number(),
    defense: v.number(),
    agility: v.number(),
    intelligence: v.number(),
    luck: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) throw new Error("Character not found");

    const totalPointsToAllocate = args.strength + args.defense + args.agility + args.intelligence + args.luck;
    
    if (totalPointsToAllocate > character.statPoints) {
      throw new Error("Not enough stat points");
    }

    if (totalPointsToAllocate < 0) {
      throw new Error("Cannot allocate negative points");
    }

    const newHealth = character.maxHealth + (args.strength * 5);
    const newMana = character.maxMana + (args.intelligence * 3);

    await ctx.db.patch(character._id, {
      strength: character.strength + args.strength,
      defense: character.defense + args.defense,
      agility: character.agility + args.agility,
      intelligence: character.intelligence + args.intelligence,
      luck: character.luck + args.luck,
      statPoints: character.statPoints - totalPointsToAllocate,
      maxHealth: newHealth,
      maxMana: newMana,
      health: Math.min(character.health, newHealth),
      mana: Math.min(character.mana, newMana),
    });

    return true;
  },
});

export const getCharacterStats = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) return null;

    // Get equipped items and calculate bonuses
    const equippedItems = await ctx.db
      .query("characterItems")
      .withIndex("by_character_equipped", (q) => 
        q.eq("characterId", character._id).eq("isEquipped", true)
      )
      .collect();

    let totalStats = {
      attack: 0,
      defense: character.defense,
      health: character.maxHealth,
      mana: character.maxMana,
      strength: character.strength,
      agility: character.agility,
      intelligence: character.intelligence,
      luck: character.luck,
    };

    // Calculate equipment bonuses
    for (const characterItem of equippedItems) {
      const item = await ctx.db.get(characterItem.itemId);
      if (item) {
        const enhancementMultiplier = 1 + (characterItem.enhancementLevel * 0.1);
        
        if (item.stats.attack) totalStats.attack += Math.floor(item.stats.attack * enhancementMultiplier);
        if (item.stats.defense) totalStats.defense += Math.floor(item.stats.defense * enhancementMultiplier);
        if (item.stats.health) totalStats.health += Math.floor(item.stats.health * enhancementMultiplier);
        if (item.stats.mana) totalStats.mana += Math.floor(item.stats.mana * enhancementMultiplier);
        if (item.stats.strength) totalStats.strength += Math.floor(item.stats.strength * enhancementMultiplier);
        if (item.stats.agility) totalStats.agility += Math.floor(item.stats.agility * enhancementMultiplier);
        if (item.stats.intelligence) totalStats.intelligence += Math.floor(item.stats.intelligence * enhancementMultiplier);
        if (item.stats.luck) totalStats.luck += Math.floor(item.stats.luck * enhancementMultiplier);
      }
    }

    return {
      ...character,
      calculatedStats: totalStats,
      equippedItems: equippedItems.length,
    };
  },
});

export const healCharacter = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) throw new Error("Character not found");

    const healCost = Math.floor((character.maxHealth - character.health) * 0.5);
    
    if (character.gold < healCost) {
      throw new Error("Not enough gold to heal");
    }

    await ctx.db.patch(character._id, {
      health: character.maxHealth,
      mana: character.maxMana,
      gold: character.gold - healCost,
    });

    return { healCost };
  },
});
