import { query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getAvailableAreas = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) return [];

    const areas = await ctx.db.query("areas").collect();
    
    return areas.map(area => ({
      ...area,
      isAccessible: character.level >= area.minLevel,
      recommendedLevel: `${area.minLevel}-${area.maxLevel}`,
    }));
  },
});

export const getAreaMonsters = query({
  args: { areaId: v.id("areas") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) return [];

    const area = await ctx.db.get(args.areaId);
    if (!area || character.level < area.minLevel) {
      throw new Error("Area not accessible");
    }

    const monsters = await ctx.db
      .query("monsters")
      .withIndex("by_area", (q) => q.eq("areaId", args.areaId))
      .collect();

    return monsters.map(monster => ({
      ...monster,
      difficulty: monster.level > character.level ? "hard" : 
                 monster.level < character.level - 5 ? "easy" : "normal",
    }));
  },
});
