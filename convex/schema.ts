import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  characters: defineTable({
    userId: v.id("users"),
    name: v.string(),
    level: v.number(),
    experience: v.number(),
    experienceToNext: v.number(),
    health: v.number(),
    maxHealth: v.number(),
    mana: v.number(),
    maxMana: v.number(),
    strength: v.number(),
    defense: v.number(),
    agility: v.number(),
    intelligence: v.number(),
    luck: v.number(),
    statPoints: v.number(),
    gold: v.number(),
    gems: v.number(),
    currentAreaId: v.optional(v.id("areas")),
    lastCombatTime: v.optional(v.number()),
    isInCombat: v.boolean(),
  }).index("by_user", ["userId"]),

  areas: defineTable({
    name: v.string(),
    description: v.string(),
    minLevel: v.number(),
    maxLevel: v.number(),
    backgroundImage: v.optional(v.string()),
  }),

  monsters: defineTable({
    name: v.string(),
    areaId: v.id("areas"),
    level: v.number(),
    health: v.number(),
    maxHealth: v.optional(v.number()),
    attack: v.number(),
    defense: v.number(),
    experience: v.number(),
    goldReward: v.number(),
    sprite: v.optional(v.string()),
  }).index("by_area", ["areaId"]),

  items: defineTable({
    name: v.string(),
    type: v.union(v.literal("weapon"), v.literal("armor"), v.literal("accessory"), v.literal("consumable")),
    rarity: v.union(v.literal("common"), v.literal("uncommon"), v.literal("rare"), v.literal("epic"), v.literal("legendary")),
    level: v.number(),
    stats: v.object({
      attack: v.optional(v.number()),
      defense: v.optional(v.number()),
      health: v.optional(v.number()),
      mana: v.optional(v.number()),
      strength: v.optional(v.number()),
      agility: v.optional(v.number()),
      intelligence: v.optional(v.number()),
      luck: v.optional(v.number()),
    }),
    value: v.number(),
    description: v.string(),
    sprite: v.optional(v.string()),
  }),

  characterItems: defineTable({
    characterId: v.id("characters"),
    itemId: v.id("items"),
    quantity: v.number(),
    isEquipped: v.boolean(),
    equipSlot: v.optional(v.union(
      v.literal("weapon"),
      v.literal("helmet"),
      v.literal("chest"),
      v.literal("legs"),
      v.literal("boots"),
      v.literal("gloves"),
      v.literal("ring1"),
      v.literal("ring2"),
      v.literal("necklace")
    )),
    enhancementLevel: v.number(),
  }).index("by_character", ["characterId"])
    .index("by_character_equipped", ["characterId", "isEquipped"]),

  combatLogs: defineTable({
    characterId: v.id("characters"),
    monsterId: v.id("monsters"),
    damage: v.number(),
    isCritical: v.boolean(),
    experienceGained: v.number(),
    goldGained: v.number(),
    itemsDropped: v.array(v.id("items")),
    timestamp: v.number(),
  }).index("by_character", ["characterId"]),

  actionTimers: defineTable({
    characterId: v.id("characters"),
    action: v.string(),
    expiresAt: v.number(),
  }).index("by_character", ["characterId"])
    .index("by_character_action", ["characterId", "action"]),

  auctionListings: defineTable({
    sellerId: v.id("characters"),
    itemId: v.id("items"),
    price: v.number(),
    currency: v.union(v.literal("gold"), v.literal("gems")),
    expiresAt: v.number(),
    isActive: v.boolean(),
  }).index("by_active", ["isActive"])
    .index("by_seller", ["sellerId"]),

  enhancementCubes: defineTable({
    name: v.string(),
    rarity: v.union(v.literal("common"), v.literal("uncommon"), v.literal("rare"), v.literal("epic"), v.literal("legendary")),
    successRate: v.number(),
    maxEnhancement: v.number(),
    cost: v.number(),
    description: v.string(),
  }),

  subscriptions: defineTable({
    userId: v.id("users"),
    planType: v.union(v.literal("basic"), v.literal("premium"), v.literal("elite")),
    isActive: v.boolean(),
    expiresAt: v.number(),
    perks: v.object({
      dropRateBonus: v.number(),
      experienceBonus: v.number(),
      cooldownReduction: v.number(),
      dailyGems: v.number(),
    }),
  }).index("by_user", ["userId"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
