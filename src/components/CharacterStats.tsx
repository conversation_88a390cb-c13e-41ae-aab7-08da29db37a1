import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

interface CharacterStatsProps {
  character: any;
}

export function CharacterStats({ character }: CharacterStatsProps) {
  const [allocations, setAllocations] = useState({
    strength: 0,
    defense: 0,
    agility: 0,
    intelligence: 0,
    luck: 0,
  });

  const allocateStats = useMutation(api.characters.allocateStatPoints);
  const healCharacter = useMutation(api.characters.healCharacter);
  const [isAllocating, setIsAllocating] = useState(false);
  const [isHealing, setIsHealing] = useState(false);

  const totalAllocated = Object.values(allocations).reduce((sum, val) => sum + val, 0);
  const remainingPoints = character.statPoints - totalAllocated;

  const handleStatChange = (stat: keyof typeof allocations, delta: number) => {
    setAllocations(prev => {
      const newValue = Math.max(0, prev[stat] + delta);
      const newTotal = Object.entries(prev).reduce((sum, [key, val]) => {
        return sum + (key === stat ? newValue : val);
      }, 0);
      
      if (newTotal <= character.statPoints) {
        return { ...prev, [stat]: newValue };
      }
      return prev;
    });
  };

  const handleAllocate = async () => {
    if (totalAllocated === 0) {
      toast.error("No stat points to allocate");
      return;
    }

    setIsAllocating(true);
    try {
      await allocateStats(allocations);
      setAllocations({ strength: 0, defense: 0, agility: 0, intelligence: 0, luck: 0 });
      toast.success("Stat points allocated successfully!");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to allocate stats");
    } finally {
      setIsAllocating(false);
    }
  };

  const handleHeal = async () => {
    if (character.health >= character.calculatedStats.health) {
      toast.error("Already at full health");
      return;
    }

    setIsHealing(true);
    try {
      const result = await healCharacter();
      toast.success(`Healed for ${result.healCost} gold`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to heal");
    } finally {
      setIsHealing(false);
    }
  };

  const healCost = Math.floor((character.calculatedStats.health - character.health) * 0.5);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Current Stats */}
      <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <h3 className="text-xl font-bold text-white mb-4">📊 Character Stats</h3>
        
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-300">💪 Strength:</span>
            <span className="text-red-400 font-bold">{character.calculatedStats.strength}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-300">🛡️ Defense:</span>
            <span className="text-blue-400 font-bold">{character.calculatedStats.defense}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-300">⚡ Agility:</span>
            <span className="text-green-400 font-bold">{character.calculatedStats.agility}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-300">🧠 Intelligence:</span>
            <span className="text-purple-400 font-bold">{character.calculatedStats.intelligence}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-300">🍀 Luck:</span>
            <span className="text-yellow-400 font-bold">{character.calculatedStats.luck}</span>
          </div>
        </div>

        <div className="mt-6 pt-4 border-t border-white/10">
          <div className="flex justify-between mb-2">
            <span className="text-gray-300">❤️ Total Health:</span>
            <span className="text-red-400 font-bold">{character.calculatedStats.health}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-gray-300">💙 Total Mana:</span>
            <span className="text-blue-400 font-bold">{character.calculatedStats.mana}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-300">⚔️ Total Attack:</span>
            <span className="text-orange-400 font-bold">{character.calculatedStats.attack}</span>
          </div>
        </div>

        {character.health < character.calculatedStats.health && (
          <div className="mt-6 pt-4 border-t border-white/10">
            <button
              onClick={handleHeal}
              disabled={isHealing || character.gold < healCost}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isHealing ? "Healing..." : `Heal (${healCost} gold)`}
            </button>
          </div>
        )}
      </div>

      {/* Stat Allocation */}
      {character.statPoints > 0 && (
        <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <h3 className="text-xl font-bold text-white mb-4">⭐ Allocate Stats</h3>
          
          <div className="mb-4">
            <p className="text-gray-300">Available Points: <span className="text-yellow-400 font-bold">{remainingPoints}</span></p>
          </div>

          <div className="space-y-4">
            {Object.entries(allocations).map(([stat, value]) => (
              <div key={stat} className="flex items-center justify-between">
                <span className="text-gray-300 capitalize">
                  {stat === "strength" && "💪"} 
                  {stat === "defense" && "🛡️"} 
                  {stat === "agility" && "⚡"} 
                  {stat === "intelligence" && "🧠"} 
                  {stat === "luck" && "🍀"} 
                  {stat}:
                </span>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleStatChange(stat as keyof typeof allocations, -1)}
                    disabled={value === 0}
                    className="w-8 h-8 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    -
                  </button>
                  <span className="w-8 text-center text-white font-bold">{value}</span>
                  <button
                    onClick={() => handleStatChange(stat as keyof typeof allocations, 1)}
                    disabled={remainingPoints === 0}
                    className="w-8 h-8 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    +
                  </button>
                </div>
              </div>
            ))}
          </div>

          <button
            onClick={handleAllocate}
            disabled={isAllocating || totalAllocated === 0}
            className="w-full mt-6 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isAllocating ? "Allocating..." : `Allocate ${totalAllocated} Points`}
          </button>
        </div>
      )}
    </div>
  );
}
