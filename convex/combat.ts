import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const initiateCombat = mutation({
  args: { monsterId: v.id("monsters") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) throw new Error("Character not found");

    if (character.isInCombat) {
      throw new Error("Already in combat");
    }

    if (character.health <= 0) {
      throw new Error("Character must be healed before combat");
    }

    // Check combat cooldown
    const now = Date.now();
    const lastCombat = character.lastCombatTime || 0;
    const cooldownTime = 3000; // 3 seconds

    if (now - lastCombat < cooldownTime) {
      throw new Error(`Combat on cooldown for ${Math.ceil((cooldownTime - (now - lastCombat)) / 1000)} seconds`);
    }

    const monster = await ctx.db.get(args.monsterId);
    if (!monster) throw new Error("Monster not found");

    // Get character's total attack power
    const equippedItems = await ctx.db
      .query("characterItems")
      .withIndex("by_character_equipped", (q) => 
        q.eq("characterId", character._id).eq("isEquipped", true)
      )
      .collect();

    let totalAttack = character.strength;
    let totalLuck = character.luck;
    let totalDefense = character.defense;

    for (const characterItem of equippedItems) {
      const item = await ctx.db.get(characterItem.itemId);
      if (item) {
        const enhancementMultiplier = 1 + (characterItem.enhancementLevel * 0.1);
        if (item.stats.attack) totalAttack += Math.floor(item.stats.attack * enhancementMultiplier);
        if (item.stats.luck) totalLuck += Math.floor(item.stats.luck * enhancementMultiplier);
        if (item.stats.defense) totalDefense += Math.floor(item.stats.defense * enhancementMultiplier);
      }
    }

    // Calculate damage dealt to monster
    const baseDamage = Math.max(1, totalAttack - monster.defense);
    const variance = Math.random() * 0.4 + 0.8; // 80-120% damage variance
    const damage = Math.floor(baseDamage * variance);
    
    // Critical hit calculation
    const critChance = Math.min(0.3, totalLuck * 0.01); // Max 30% crit chance
    const isCritical = Math.random() < critChance;
    const finalDamage = isCritical ? Math.floor(damage * 1.5) : damage;

    // Monster takes damage but might survive
    const monsterRemainingHealth = Math.max(0, monster.health - finalDamage);
    const monsterDefeated = monsterRemainingHealth <= 0;

    let experienceGained = 0;
    let goldGained = 0;
    let characterTookDamage = 0;
    let newLevel = character.level;
    let newStatPoints = character.statPoints;
    let experienceToNext = character.experienceToNext;
    let newCharacterHealth = character.health;

    if (monsterDefeated) {
      // Monster is defeated - player gets full rewards
      experienceGained = monster.experience;
      goldGained = monster.goldReward;
      
      // Get the original max health from seed data based on monster name
      let originalMaxHealth;
      switch (monster.name) {
        case "Forest Goblin":
          originalMaxHealth = 25;
          break;
        case "Wild Wolf":
          originalMaxHealth = 45;
          break;
        case "Forest Troll":
          originalMaxHealth = 80;
          break;
        case "Cave Bat":
          originalMaxHealth = 60;
          break;
        case "Stone Golem":
          originalMaxHealth = 120;
          break;
        case "Cave Dragon":
          originalMaxHealth = 200;
          break;
        case "Ice Elemental":
          originalMaxHealth = 180;
          break;
        case "Frost Giant":
          originalMaxHealth = 300;
          break;
        case "Ancient Yeti":
          originalMaxHealth = 500;
          break;
        default:
          // Fallback to maxHealth if set, otherwise use current health
          originalMaxHealth = monster.maxHealth || monster.health;
      }
      
      await ctx.db.patch(args.monsterId, {
        health: originalMaxHealth,
        maxHealth: originalMaxHealth, // Ensure maxHealth is set for future fights
      });
    } else {
      // Monster survives and attacks back
      const monsterDamage = Math.max(1, monster.attack - totalDefense);
      const monsterVariance = Math.random() * 0.4 + 0.8;
      characterTookDamage = Math.floor(monsterDamage * monsterVariance);
      newCharacterHealth = Math.max(0, character.health - characterTookDamage);
      
      // Partial rewards for surviving the exchange
      experienceGained = Math.floor(monster.experience * 0.1);
      goldGained = Math.floor(monster.goldReward * 0.1);
      
      // Get the original max health for proper maxHealth setting
      let originalMaxHealth;
      switch (monster.name) {
        case "Forest Goblin":
          originalMaxHealth = 25;
          break;
        case "Wild Wolf":
          originalMaxHealth = 45;
          break;
        case "Forest Troll":
          originalMaxHealth = 80;
          break;
        case "Cave Bat":
          originalMaxHealth = 60;
          break;
        case "Stone Golem":
          originalMaxHealth = 120;
          break;
        case "Cave Dragon":
          originalMaxHealth = 200;
          break;
        case "Ice Elemental":
          originalMaxHealth = 180;
          break;
        case "Frost Giant":
          originalMaxHealth = 300;
          break;
        case "Ancient Yeti":
          originalMaxHealth = 500;
          break;
        default:
          originalMaxHealth = monster.maxHealth || monster.health;
      }
      
      await ctx.db.patch(args.monsterId, {
        health: monsterRemainingHealth,
        maxHealth: originalMaxHealth,
      });
    }

    // Check for level up
    const newExperience = character.experience + experienceGained;
    if (newExperience >= character.experienceToNext) {
      newLevel++;
      newStatPoints += 5; // 5 stat points per level
      experienceToNext = newLevel * 100; // Simple formula
    }

    // Update character
    await ctx.db.patch(character._id, {
      experience: newExperience,
      level: newLevel,
      statPoints: newStatPoints,
      experienceToNext: experienceToNext,
      gold: character.gold + goldGained,
      health: newCharacterHealth,
      lastCombatTime: now,
      isInCombat: false,
    });

    // Log combat
    await ctx.db.insert("combatLogs", {
      characterId: character._id,
      monsterId: args.monsterId,
      damage: finalDamage,
      isCritical,
      experienceGained,
      goldGained,
      itemsDropped: [], // TODO: Implement item drops
      timestamp: now,
    });

    return {
      damage: finalDamage,
      isCritical,
      experienceGained,
      goldGained,
      leveledUp: newLevel > character.level,
      newLevel,
      monster: monster.name,
      monsterDefeated,
      monsterRemainingHealth,
      characterTookDamage,
      characterHealth: newCharacterHealth,
    };
  },
});

export const getCombatHistory = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) return [];

    const combatLogs = await ctx.db
      .query("combatLogs")
      .withIndex("by_character", (q) => q.eq("characterId", character._id))
      .order("desc")
      .take(20);

    const logsWithMonsters = await Promise.all(
      combatLogs.map(async (log) => {
        const monster = await ctx.db.get(log.monsterId);
        return {
          ...log,
          monsterName: monster?.name || "Unknown Monster",
        };
      })
    );

    return logsWithMonsters;
  },
});

export const getCombatCooldown = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return { isOnCooldown: false, remainingTime: 0 };

    const character = await ctx.db
      .query("characters")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!character) return { isOnCooldown: false, remainingTime: 0 };

    const now = Date.now();
    const lastCombat = character.lastCombatTime || 0;
    const cooldownTime = 3000; // 3 seconds
    const remainingTime = Math.max(0, cooldownTime - (now - lastCombat));


    return {
      isOnCooldown: remainingTime > 0,
      remainingTime: Math.ceil(remainingTime / 1000),
    };
  },
});
