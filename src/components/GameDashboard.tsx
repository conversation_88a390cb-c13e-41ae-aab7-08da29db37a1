import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { CharacterStats } from "./CharacterStats";
import { CombatArea } from "./CombatArea";
import { InventoryPanel } from "./InventoryPanel";
import { CombatLog } from "./CombatLog";
import { useState, useEffect } from "react";

export function GameDashboard() {
  const character = useQuery(api.characters.getCharacterStats);
  const [activeTab, setActiveTab] = useState<"combat" | "inventory" | "stats">("combat");
  const seedData = useMutation(api.seedData.seedGameData);

  // Seed game data on first load
  useEffect(() => {
    seedData().catch(() => {
      // Data already seeded, ignore error
    });
  }, [seedData]);

  if (!character) {
    return (
      <div className="flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>
    );
  }

  const tabs = [
    { id: "combat" as const, label: "⚔️ Combat", icon: "⚔️" },
    { id: "inventory" as const, label: "🎒 Inventory", icon: "🎒" },
    { id: "stats" as const, label: "📊 Stats", icon: "📊" },
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Character Overview - Always Visible */}
      <div className="lg:col-span-1">
        <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
          <div className="text-center mb-4">
            <h2 className="text-2xl font-bold text-white">{character.name}</h2>
            <p className="text-gray-300">Level {character.level}</p>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-300">Health:</span>
              <span className="text-red-400">{character.health}/{character.calculatedStats.health}</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-red-500 h-2 rounded-full transition-all"
                style={{ width: `${(character.health / character.calculatedStats.health) * 100}%` }}
              />
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-300">Mana:</span>
              <span className="text-blue-400">{character.mana}/{character.calculatedStats.mana}</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all"
                style={{ width: `${(character.mana / character.calculatedStats.mana) * 100}%` }}
              />
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-300">Experience:</span>
              <span className="text-yellow-400">{character.experience}/{character.experienceToNext}</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-yellow-500 h-2 rounded-full transition-all"
                style={{ width: `${(character.experience / character.experienceToNext) * 100}%` }}
              />
            </div>

            <div className="pt-4 border-t border-white/10">
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-300">💰 Gold:</span>
                <span className="text-yellow-400">{character.gold.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">💎 Gems:</span>
                <span className="text-purple-400">{character.gems.toLocaleString()}</span>
              </div>
            </div>

            {character.statPoints > 0 && (
              <div className="pt-2 border-t border-white/10">
                <p className="text-center text-green-400 text-sm">
                  ⭐ {character.statPoints} stat points available!
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="lg:col-span-3">
        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-all ${
                activeTab === tab.id
                  ? "bg-blue-600 text-white shadow-lg"
                  : "bg-black/20 text-gray-300 hover:bg-black/30"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === "combat" && (
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              <div className="xl:col-span-2">
                <CombatArea />
              </div>
              <div>
                <CombatLog />
              </div>
            </div>
          )}
          
          {activeTab === "inventory" && <InventoryPanel />}
          
          {activeTab === "stats" && <CharacterStats character={character} />}
        </div>
      </div>
    </div>
  );
}
