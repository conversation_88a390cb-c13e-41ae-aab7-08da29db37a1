import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

export function InventoryPanel() {
  const inventory = useQuery(api.items.getCharacterInventory);
  const equipItem = useMutation(api.items.equipItem);
  const unequipItem = useMutation(api.items.unequipItem);

  const handleEquip = async (characterItemId: string, equipSlot: string) => {
    try {
      await equipItem({ 
        characterItemId: characterItemId as any, 
        equipSlot: equipSlot as any 
      });
      toast.success("Item equipped!");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to equip item");
    }
  };

  const handleUnequip = async (characterItemId: string) => {
    try {
      await unequipItem({ characterItemId: characterItemId as any });
      toast.success("Item unequipped!");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to unequip item");
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case "common": return "text-gray-400 border-gray-500";
      case "uncommon": return "text-green-400 border-green-500";
      case "rare": return "text-blue-400 border-blue-500";
      case "epic": return "text-purple-400 border-purple-500";
      case "legendary": return "text-yellow-400 border-yellow-500";
      default: return "text-gray-400 border-gray-500";
    }
  };

  const getEquipSlots = (itemType: string) => {
    switch (itemType) {
      case "weapon": return ["weapon"];
      case "armor": return ["helmet", "chest", "legs", "boots", "gloves"];
      case "accessory": return ["ring1", "ring2", "necklace"];
      default: return [];
    }
  };

  if (!inventory) {
    return (
      <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <div className="animate-pulse">Loading inventory...</div>
      </div>
    );
  }

  const equippedItems = inventory.filter(item => item.isEquipped);
  const unequippedItems = inventory.filter(item => !item.isEquipped);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Equipped Items */}
      <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <h3 className="text-xl font-bold text-white mb-4">⚔️ Equipped Items</h3>
        
        {equippedItems.length === 0 ? (
          <p className="text-gray-400">No items equipped</p>
        ) : (
          <div className="space-y-3">
            {equippedItems.map((characterItem) => (
              <div
                key={characterItem._id}
                className={`p-4 rounded-lg border-2 ${getRarityColor(characterItem.item!.rarity)}`}
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-semibold text-white">{characterItem.item!.name}</h4>
                    <p className="text-sm text-gray-300 capitalize">
                      {characterItem.equipSlot} • {characterItem.item!.rarity}
                    </p>
                  </div>
                  {characterItem.enhancementLevel > 0 && (
                    <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded">
                      +{characterItem.enhancementLevel}
                    </span>
                  )}
                </div>

                <div className="text-sm text-gray-300 mb-3">
                  {characterItem.item!.description}
                </div>

                {/* Stats */}
                <div className="text-sm space-y-1 mb-3">
                  {Object.entries(characterItem.item!.stats).map(([stat, value]) => {
                    if (!value) return null;
                    const enhancedValue = Math.floor(value * (1 + characterItem.enhancementLevel * 0.1));
                    return (
                      <div key={stat} className="flex justify-between">
                        <span className="text-gray-300 capitalize">{stat}:</span>
                        <span className="text-green-400">+{enhancedValue}</span>
                      </div>
                    );
                  })}
                </div>

                <button
                  onClick={() => handleUnequip(characterItem._id)}
                  className="w-full px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
                >
                  Unequip
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Inventory Items */}
      <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-white/20">
        <h3 className="text-xl font-bold text-white mb-4">🎒 Inventory</h3>
        
        {unequippedItems.length === 0 ? (
          <p className="text-gray-400">No items in inventory</p>
        ) : (
          <div className="space-y-3">
            {unequippedItems.map((characterItem) => (
              <div
                key={characterItem._id}
                className={`p-4 rounded-lg border ${getRarityColor(characterItem.item!.rarity)}`}
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-semibold text-white">{characterItem.item!.name}</h4>
                    <p className="text-sm text-gray-300 capitalize">
                      {characterItem.item!.type} • {characterItem.item!.rarity}
                    </p>
                  </div>
                  {characterItem.enhancementLevel > 0 && (
                    <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded">
                      +{characterItem.enhancementLevel}
                    </span>
                  )}
                </div>

                <div className="text-sm text-gray-300 mb-3">
                  {characterItem.item!.description}
                </div>

                {/* Stats */}
                <div className="text-sm space-y-1 mb-3">
                  {Object.entries(characterItem.item!.stats).map(([stat, value]) => {
                    if (!value) return null;
                    const enhancedValue = Math.floor(value * (1 + characterItem.enhancementLevel * 0.1));
                    return (
                      <div key={stat} className="flex justify-between">
                        <span className="text-gray-300 capitalize">{stat}:</span>
                        <span className="text-green-400">+{enhancedValue}</span>
                      </div>
                    );
                  })}
                </div>

                {/* Equip Options */}
                {characterItem.item!.type !== "consumable" && (
                  <div className="space-y-2">
                    {getEquipSlots(characterItem.item!.type).map((slot) => (
                      <button
                        key={slot}
                        onClick={() => handleEquip(characterItem._id, slot)}
                        className="w-full px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                      >
                        Equip to {slot}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
